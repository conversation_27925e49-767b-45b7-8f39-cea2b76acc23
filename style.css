* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
}

.container {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 100%;
}

h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #2c3e50;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
}

.player-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.current-player {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: bold;
    font-size: 1.1em;
}

.player-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.score {
    display: flex;
    gap: 20px;
}

.score-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    font-size: 1.2em;
}

.piece {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid #333;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.piece.black {
    background: #2c3e50;
}

.piece.white {
    background: #ecf0f1;
}

.restart-btn {
    padding: 12px 24px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.restart-btn:hover {
    background: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.game-board {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(8, 1fr);
    gap: 2px;
    background: #2c3e50;
    padding: 10px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.cell {
    width: 50px;
    height: 50px;
    background: #27ae60;
    border: 1px solid #229954;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;
}

.cell:hover {
    background: #2ecc71;
    transform: scale(1.05);
}

.cell.valid-move {
    background: #f39c12;
    box-shadow: 0 0 10px rgba(243, 156, 18, 0.6);
}

.cell.valid-move:hover {
    background: #e67e22;
}

.cell .piece {
    width: 40px;
    height: 40px;
    transition: all 0.3s ease;
}

.cell .piece.flipping {
    animation: flip 0.6s ease-in-out;
}

@keyframes flip {
    0% { transform: scaleX(1); }
    50% { transform: scaleX(0); }
    100% { transform: scaleX(1); }
}

.game-status {
    text-align: center;
    font-size: 1.2em;
    font-weight: bold;
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 10px;
    background: #e8f5e8;
    color: #27ae60;
}

.game-status.warning {
    background: #fff3cd;
    color: #856404;
}

.game-status.game-over {
    background: #f8d7da;
    color: #721c24;
}

.rules {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 15px;
    margin-top: 20px;
}

.rules h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.rules ul {
    list-style-position: inside;
    line-height: 1.6;
}

.rules li {
    margin-bottom: 8px;
    color: #555;
}

@media (max-width: 768px) {
    .container {
        padding: 20px;
        margin: 20px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    .game-info {
        flex-direction: column;
        gap: 15px;
    }
    
    .cell {
        width: 40px;
        height: 40px;
    }
    
    .cell .piece {
        width: 32px;
        height: 32px;
    }
}
