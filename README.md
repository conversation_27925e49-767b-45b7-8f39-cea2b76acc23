# 黑白棋游戏 (Othello/Reversi)

一个基于HTML、CSS和JavaScript开发的黑白棋小游戏。

## 游戏特性

- 🎮 完整的黑白棋游戏逻辑
- 🤖 智能AI对手，支持人机对战
- 👥 人人对战模式
- 🎨 现代简约的用户界面设计
- 📱 响应式设计，完美支持移动端
- 🔄 实时分数显示
- ✨ 流畅的动画效果和微交互
- 🎯 智能的合法落子位置提示
- 🧠 AI思考过程可视化
- 🎪 柔和的配色方案和渐变效果

## 游戏规则

1. **棋盘**: 8×8的方格棋盘
2. **初始状态**: 棋盘中央放置4枚棋子（2黑2白，对角排列）
3. **下棋规则**: 
   - 黑棋先行，双方轮流下棋
   - 必须在能夹住对方棋子的位置下棋
   - 被夹住的对方棋子全部翻转为己方颜色
4. **跳过回合**: 如果无法下棋则自动跳过回合
5. **游戏结束**: 棋盘填满或双方都无法下棋时游戏结束
6. **胜负判定**: 棋子数量多的一方获胜

## 文件结构

```
heibaiqi/
├── index.html      # 主页面
├── style.css       # 样式文件
├── script.js       # 游戏逻辑
└── README.md       # 项目说明
```

## 如何运行

1. 直接在浏览器中打开 `index.html` 文件
2. 或者使用本地服务器：
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 使用Node.js
   npx serve .
   ```

## 技术实现

### 核心类: OthelloGame

- **棋盘表示**: 使用8×8的二维数组，1表示黑棋，-1表示白棋，0表示空位
- **合法性检查**: 检查8个方向是否能夹住对方棋子
- **棋子翻转**: 在所有有效方向上翻转被夹住的棋子
- **游戏状态**: 跟踪当前玩家、分数、游戏模式和结束状态
- **AI算法**: 基于位置权重和贪心策略的智能AI

### 主要功能

1. **游戏模式管理**:
   - `showModeSelection()`: 显示模式选择界面
   - `startGame(mode)`: 开始指定模式的游戏

2. **核心游戏逻辑**:
   - `initializeBoard()`: 初始化棋盘状态
   - `isValidMove()`: 检查落子是否合法
   - `makeMove()`: 执行落子并翻转棋子
   - `processMove()`: 处理移动后的游戏状态
   - `hasValidMoves()`: 检查是否有可下的棋

3. **AI系统**:
   - `aiMove()`: AI执行移动（带思考延迟）
   - `getBestMove()`: 获取AI的最佳移动
   - `evaluateMove()`: 评估移动的价值
   - `showAiThinking()`: 显示AI思考状态

4. **界面更新**:
   - `updateUI()`: 更新界面显示
   - `showValidMoves()`: 显示可下棋位置
   - `updateScore()`: 更新分数显示

## AI算法特性

### 评估策略
1. **位置权重**: 角落(100分) > 边缘(50分) > 内部(-1到10分)
2. **翻转数量**: 每翻转一个棋子+10分
3. **移动性**: 减少对手可选移动数量
4. **稳定性**: 优先占据角落和边缘位置

### AI行为
- 思考时间: 0.8-1.2秒的随机延迟
- 难度适中: 既有挑战性又不会过于困难
- 遵循所有游戏规则，包括跳过回合逻辑

## 界面特性

- **现代设计**: 采用毛玻璃效果和柔和渐变
- **响应式布局**: 完美适配手机、平板和桌面
- **视觉反馈**:
  - 高亮显示可下棋位置（脉冲动画）
  - 棋子翻转动画效果
  - 悬停和点击反馈
- **实时更新**: 分数和当前玩家实时显示
- **游戏状态**: 清晰的状态提示和结果显示
- **AI指示器**: 显示AI思考过程的动画

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 游戏模式

### 人人对战 (PvP)
- 两个玩家轮流下棋
- 适合朋友间对战
- 完整的游戏规则实现

### 人机对战 (PvE)
- 与智能AI对战
- AI默认为白棋，玩家为黑棋
- AI具有合理的思考时间和策略

## 未来改进

- [x] ~~添加AI对战功能~~
- [ ] 增加AI难度选择（简单/中等/困难）
- [ ] 添加游戏历史记录和回放
- [ ] 支持悔棋功能
- [ ] 添加音效和背景音乐
- [ ] 多语言支持
- [ ] 在线对战功能
- [ ] 游戏统计和成就系统

## 许可证

MIT License
