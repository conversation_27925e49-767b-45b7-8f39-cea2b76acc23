# 黑白棋游戏 (Othello/Reversi)

一个基于HTML、CSS和JavaScript开发的黑白棋小游戏。

## 游戏特性

- 🎮 完整的黑白棋游戏逻辑
- 🎨 美观的用户界面设计
- 📱 响应式设计，支持移动端
- 🔄 实时分数显示
- ✨ 流畅的动画效果
- 🎯 合法落子位置提示

## 游戏规则

1. **棋盘**: 8×8的方格棋盘
2. **初始状态**: 棋盘中央放置4枚棋子（2黑2白，对角排列）
3. **下棋规则**: 
   - 黑棋先行，双方轮流下棋
   - 必须在能夹住对方棋子的位置下棋
   - 被夹住的对方棋子全部翻转为己方颜色
4. **跳过回合**: 如果无法下棋则自动跳过回合
5. **游戏结束**: 棋盘填满或双方都无法下棋时游戏结束
6. **胜负判定**: 棋子数量多的一方获胜

## 文件结构

```
heibaiqi/
├── index.html      # 主页面
├── style.css       # 样式文件
├── script.js       # 游戏逻辑
└── README.md       # 项目说明
```

## 如何运行

1. 直接在浏览器中打开 `index.html` 文件
2. 或者使用本地服务器：
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 使用Node.js
   npx serve .
   ```

## 技术实现

### 核心类: OthelloGame

- **棋盘表示**: 使用8×8的二维数组，1表示黑棋，-1表示白棋，0表示空位
- **合法性检查**: 检查8个方向是否能夹住对方棋子
- **棋子翻转**: 在所有有效方向上翻转被夹住的棋子
- **游戏状态**: 跟踪当前玩家、分数和游戏结束状态

### 主要功能

1. **initializeBoard()**: 初始化棋盘状态
2. **isValidMove()**: 检查落子是否合法
3. **makeMove()**: 执行落子并翻转棋子
4. **updateUI()**: 更新界面显示
5. **hasValidMoves()**: 检查是否有可下的棋

## 界面特性

- **响应式设计**: 适配不同屏幕尺寸
- **视觉反馈**: 高亮显示可下棋位置
- **实时更新**: 分数和当前玩家实时显示
- **游戏状态**: 显示跳过回合和游戏结束信息

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 未来改进

- [ ] 添加AI对战功能
- [ ] 增加难度选择
- [ ] 添加游戏历史记录
- [ ] 支持悔棋功能
- [ ] 添加音效
- [ ] 多语言支持

## 许可证

MIT License
