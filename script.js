class OthelloGame {
    constructor() {
        this.board = Array(8).fill().map(() => Array(8).fill(0));
        this.currentPlayer = 1; // 1 = 黑棋, -1 = 白棋
        this.gameOver = false;
        this.directions = [
            [-1, -1], [-1, 0], [-1, 1],
            [0, -1],           [0, 1],
            [1, -1],  [1, 0],  [1, 1]
        ];
        
        this.initializeBoard();
        this.createBoardUI();
        this.updateUI();
        this.bindEvents();
    }
    
    initializeBoard() {
        // 初始化中央4个棋子
        this.board[3][3] = -1; // 白棋
        this.board[3][4] = 1;  // 黑棋
        this.board[4][3] = 1;  // 黑棋
        this.board[4][4] = -1; // 白棋
    }
    
    createBoardUI() {
        const gameBoard = document.getElementById('gameBoard');
        gameBoard.innerHTML = '';
        
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                cell.dataset.row = row;
                cell.dataset.col = col;
                
                cell.addEventListener('click', () => this.handleCellClick(row, col));
                gameBoard.appendChild(cell);
            }
        }
    }
    
    updateUI() {
        const cells = document.querySelectorAll('.cell');
        cells.forEach(cell => {
            const row = parseInt(cell.dataset.row);
            const col = parseInt(cell.dataset.col);
            const value = this.board[row][col];
            
            cell.innerHTML = '';
            cell.classList.remove('valid-move');
            
            if (value !== 0) {
                const piece = document.createElement('div');
                piece.className = `piece ${value === 1 ? 'black' : 'white'}`;
                cell.appendChild(piece);
            }
        });
        
        // 显示可下棋的位置
        if (!this.gameOver) {
            this.showValidMoves();
        }
        
        this.updateScore();
        this.updateCurrentPlayer();
        this.updateGameStatus();
    }
    
    showValidMoves() {
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (this.isValidMove(row, col, this.currentPlayer)) {
                    const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                    cell.classList.add('valid-move');
                }
            }
        }
    }
    
    isValidMove(row, col, player) {
        if (this.board[row][col] !== 0) return false;
        
        for (let [dx, dy] of this.directions) {
            if (this.checkDirection(row, col, dx, dy, player)) {
                return true;
            }
        }
        return false;
    }
    
    checkDirection(row, col, dx, dy, player) {
        let x = row + dx;
        let y = col + dy;
        let hasOpponent = false;
        
        while (x >= 0 && x < 8 && y >= 0 && y < 8) {
            if (this.board[x][y] === 0) return false;
            if (this.board[x][y] === -player) {
                hasOpponent = true;
            } else if (this.board[x][y] === player) {
                return hasOpponent;
            }
            x += dx;
            y += dy;
        }
        return false;
    }
    
    makeMove(row, col) {
        if (!this.isValidMove(row, col, this.currentPlayer)) return false;
        
        this.board[row][col] = this.currentPlayer;
        
        // 翻转棋子
        for (let [dx, dy] of this.directions) {
            this.flipDirection(row, col, dx, dy, this.currentPlayer);
        }
        
        return true;
    }
    
    flipDirection(row, col, dx, dy, player) {
        if (!this.checkDirection(row, col, dx, dy, player)) return;
        
        let x = row + dx;
        let y = col + dy;
        const toFlip = [];
        
        while (x >= 0 && x < 8 && y >= 0 && y < 8) {
            if (this.board[x][y] === 0) break;
            if (this.board[x][y] === -player) {
                toFlip.push([x, y]);
            } else if (this.board[x][y] === player) {
                // 翻转所有中间的棋子
                toFlip.forEach(([fx, fy]) => {
                    this.board[fx][fy] = player;
                });
                break;
            }
            x += dx;
            y += dy;
        }
    }
    
    hasValidMoves(player) {
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (this.isValidMove(row, col, player)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    handleCellClick(row, col) {
        if (this.gameOver) return;
        
        if (this.makeMove(row, col)) {
            this.updateUI();
            
            // 检查下一个玩家是否有可下的棋
            const nextPlayer = -this.currentPlayer;
            if (this.hasValidMoves(nextPlayer)) {
                this.currentPlayer = nextPlayer;
            } else if (!this.hasValidMoves(this.currentPlayer)) {
                // 双方都无法下棋，游戏结束
                this.gameOver = true;
            }
            // 如果只有当前玩家无法下棋，则跳过回合，继续当前玩家
            
            this.updateUI();
        }
    }
    
    updateScore() {
        let blackCount = 0;
        let whiteCount = 0;
        
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (this.board[row][col] === 1) blackCount++;
                else if (this.board[row][col] === -1) whiteCount++;
            }
        }
        
        document.getElementById('blackScore').textContent = blackCount;
        document.getElementById('whiteScore').textContent = whiteCount;
    }
    
    updateCurrentPlayer() {
        const indicator = document.getElementById('currentPlayer');
        const piece = indicator.querySelector('.piece');
        const text = indicator.querySelector('span');
        
        if (this.currentPlayer === 1) {
            piece.className = 'piece black';
            text.textContent = '黑棋';
        } else {
            piece.className = 'piece white';
            text.textContent = '白棋';
        }
    }
    
    updateGameStatus() {
        const status = document.getElementById('gameStatus');
        
        if (this.gameOver) {
            const blackScore = parseInt(document.getElementById('blackScore').textContent);
            const whiteScore = parseInt(document.getElementById('whiteScore').textContent);
            
            let message;
            if (blackScore > whiteScore) {
                message = `游戏结束！黑棋获胜！(${blackScore} : ${whiteScore})`;
            } else if (whiteScore > blackScore) {
                message = `游戏结束！白棋获胜！(${whiteScore} : ${blackScore})`;
            } else {
                message = `游戏结束！平局！(${blackScore} : ${whiteScore})`;
            }
            
            status.textContent = message;
            status.className = 'game-status game-over';
        } else if (!this.hasValidMoves(this.currentPlayer)) {
            status.textContent = `${this.currentPlayer === 1 ? '黑棋' : '白棋'}无法下棋，跳过回合`;
            status.className = 'game-status warning';
        } else {
            status.textContent = '';
            status.className = 'game-status';
        }
    }
    
    restart() {
        this.board = Array(8).fill().map(() => Array(8).fill(0));
        this.currentPlayer = 1;
        this.gameOver = false;
        this.initializeBoard();
        this.updateUI();
    }
    
    bindEvents() {
        document.getElementById('restartBtn').addEventListener('click', () => {
            this.restart();
        });
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    new OthelloGame();
});
