<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>黑白棋游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>黑白棋游戏</h1>
        
        <div class="game-info">
            <div class="player-info">
                <div class="current-player">
                    <span>当前玩家：</span>
                    <div class="player-indicator" id="currentPlayer">
                        <div class="piece black"></div>
                        <span>黑棋</span>
                    </div>
                </div>
                
                <div class="score">
                    <div class="score-item">
                        <div class="piece black"></div>
                        <span id="blackScore">2</span>
                    </div>
                    <div class="score-item">
                        <div class="piece white"></div>
                        <span id="whiteScore">2</span>
                    </div>
                </div>
            </div>
            
            <button id="restartBtn" class="restart-btn">重新开始</button>
        </div>
        
        <div class="game-board" id="gameBoard">
            <!-- 棋盘将通过JavaScript动态生成 -->
        </div>
        
        <div class="game-status" id="gameStatus">
            <!-- 游戏状态信息 -->
        </div>
        
        <div class="rules">
            <h3>游戏规则：</h3>
            <ul>
                <li>黑棋先行，双方轮流下棋</li>
                <li>下棋时必须夹住对方的棋子</li>
                <li>被夹住的棋子会翻转为己方颜色</li>
                <li>如果无法下棋则跳过回合</li>
                <li>游戏结束时棋子多的一方获胜</li>
            </ul>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
